/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { LineBreaker } from './line-breaker';
import { FontCache } from './shaping-engine/font-cache';

export interface ILineInfo {
    text: string;
    width: number;
    height: number;
    baseline: number;
}

export class DocSimpleSkeleton {
    private _lineBreaker: LineBreaker;
    private _lines: ILineInfo[] = [];
    private _dirty = true;
    private _lastBreakLength = 0; // Cache for the last successful break length

    constructor(
        private _text: string,
        private _fontStyle: string,
        private _warp: boolean,
        private _width: number,
        private _height: number
    ) {
        this._lineBreaker = new LineBreaker(this._text);
    }

    calculate() {
        if (!this._dirty) {
            return this._lines;
        }
        this._dirty = false;
        this._lines = [];
        if (this._text.length === 0) {
            const textSize = FontCache.getMeasureText('A', this._fontStyle);
            this._lines.push({
                text: '',
                width: 0,
                height: textSize.fontBoundingBoxAscent + textSize.fontBoundingBoxDescent,
                baseline: textSize.fontBoundingBoxAscent,
            });
            return this._lines;
        }

        // 强制禁用换行 - 永远都不换行
        // Force disable text wrapping - never wrap text
        const textSize = FontCache.getMeasureText(this._text, this._fontStyle);
        this._lines.push({
            text: this._text,
            width: textSize.width,
            height: textSize.fontBoundingBoxAscent + textSize.fontBoundingBoxDescent,
            baseline: textSize.fontBoundingBoxAscent,
        });
        return this._lines;

    }

    getLines() {
        return this._lines;
    }

    getTotalHeight() {
        return this._lines.reduce((acc, line) => acc + line.height, 0);
    }

    getTotalWidth() {
        return this._lines.reduce((acc, line) => Math.max(acc, line.width), 0);
    }

    makeDirty() {
        this._dirty = true;
        this._lastBreakLength = 0; // Reset cache when content changes
    }
}
